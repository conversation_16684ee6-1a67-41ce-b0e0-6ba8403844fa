# Blackjack Player Detection and Seat Management System

## Overview

I have successfully implemented a comprehensive player detection and seat management system for your blackjack card counting application. The system now includes:

1. **Player Recognition**: Detects when players are seated vs empty seats
2. **Fixed Player Seat Positions**: Configurable detection boxes for 2-5 player seats
3. **Card Detection per Seat**: Individual card tracking for each player with stacked detection
4. **Hit/Stand Button Recognition**: Detects red (hit) and green (stand) action buttons
5. **Dealer Hand Detection**: Separate detection area for dealer cards

## New Features Implemented

### 1. Player Detection (`player_detector.py`)
- **Player Name Recognition**: Uses OCR to detect player names in designated areas
- **Seat Occupation Detection**: Determines if a seat is occupied based on text presence
- **Action Button Detection**: Recognizes hit (red) and stand (green) buttons using color detection
- **Real-time State Tracking**: Maintains current state of all players

### 2. Seat Management (`seat_manager.py`)
- **Individual Seat Card Detection**: Tracks cards for each of the 5 player seats
- **Dealer Card Detection**: Separate tracking for dealer cards
- **Stacked Card Detection**: Up to 5 cards per seat with overlapping positions
- **Duplicate Prevention**: Prevents counting the same card multiple times
- **History Management**: Maintains detection history to avoid false positives

### 3. Enhanced GUI (`gui.py`)
- **Player Seats Monitor**: New display section showing all 5 seats
- **Real-time Status Updates**: Shows occupied/empty status for each seat
- **Player Names Display**: Shows detected player names
- **Action Status**: Displays current hit/stand actions
- **Cards per Seat**: Shows detected cards for each player and dealer
- **Enhanced Debug Mode**: Multiple debug windows for different detection types

### 4. Configuration System (`config.py`)
- **Seat Position Configuration**: Precise positioning for each seat's detection areas
- **Card Region Stacking**: Overlapping card positions for natural card placement
- **Action Button Areas**: Defined regions for hit/stand button detection
- **Dealer Configuration**: Separate dealer card detection areas
- **Color Detection Settings**: HSV color ranges for button recognition

### 5. Calibration Tool (`seat_calibrator.py`)
- **Interactive Position Setup**: Visual tool to adjust seat positions
- **Real-time Preview**: See detection areas overlaid on live screen capture
- **Save/Load Configuration**: Persistent calibration settings
- **Easy Adjustment**: Click and drag to define detection regions

## How It Works

### Player Detection Process
1. **Screen Capture**: Captures the blackjack table area
2. **Player Recognition**: Scans name regions for text to detect occupied seats
3. **Action Detection**: Analyzes action button areas for red/green colors
4. **Card Detection**: Checks each card position for new cards
5. **State Updates**: Updates GUI with current player states and cards

### Card Detection per Seat
- Each seat has 5 card detection regions positioned to overlap naturally
- Cards are detected using OCR in specific regions
- Duplicate detection prevents counting the same card multiple times
- History tracking maintains accuracy over time

### Action Button Recognition
- Uses HSV color space for robust color detection
- Red buttons (hit) detected in specific HSV range
- Green buttons (stand) detected in different HSV range
- Minimum pixel threshold prevents false positives

## Configuration

### Seat Positions (in `config.py`)
```python
PLAYER_SEATS = {
    'seat_1': {
        'name_region': {'x': 50, 'y': 650, 'width': 150, 'height': 40},
        'card_regions': [
            {'x': 80, 'y': 500, 'width': 90, 'height': 120},   # Card 1
            {'x': 100, 'y': 500, 'width': 90, 'height': 120},  # Card 2 (overlapped)
            # ... up to 5 cards
        ],
        'action_region': {'x': 80, 'y': 620, 'width': 80, 'height': 80}
    },
    # ... seats 2-5 with similar structure
}
```

### Dealer Configuration
```python
DEALER_CONFIG = {
    'card_regions': [
        {'x': 600, 'y': 200, 'width': 90, 'height': 120},  # Dealer card 1
        {'x': 620, 'y': 200, 'width': 90, 'height': 120},  # Dealer card 2
        # ... up to 5 dealer cards
    ]
}
```

## Usage Instructions

### 1. Basic Operation
- Run `python gui.py` to start the application
- Click "Start Detection" to begin monitoring
- The Player Seats Monitor will show real-time status

### 2. Calibrating Seat Positions
- Run `python seat_calibrator.py` to adjust positions
- Click and drag to define detection areas
- Press 'n' to move to next region
- Press 's' to save calibration
- Press 'q' to quit

### 3. Debug Mode
- Click "Show Debug" in the main GUI
- Two debug windows will appear:
  - Player Detection Debug: Shows name and action regions
  - Seat Card Detection Debug: Shows card detection areas
- Green boxes indicate successful detection
- Red boxes indicate no detection

## GUI Display Features

### Player Seats Monitor
Each seat displays:
- **Status**: Occupied (green) or Empty (red)
- **Player**: Detected player name or "None"
- **Action**: HIT (red), STAND (green), or None (gray)
- **Cards**: List of detected cards for that player

### Dealer Display
- **Cards**: List of detected dealer cards

## Technical Details

### Detection Accuracy
- Uses EasyOCR for robust text recognition
- Color detection in HSV space for lighting independence
- Temporal filtering to prevent duplicate detections
- Configurable confidence thresholds

### Performance
- Runs at ~5 FPS for real-time detection
- CPU-based processing (GPU acceleration available)
- Minimal memory footprint
- Thread-safe GUI updates

## Files Modified/Created

### New Files
- `player_detector.py` - Player recognition and action detection
- `seat_manager.py` - Seat-specific card detection
- `seat_calibrator.py` - Position calibration tool
- `test_player_detection.py` - Testing script

### Modified Files
- `gui.py` - Enhanced with player monitoring display
- `config.py` - Added seat and detection configurations

## Testing

Run the test script to verify functionality:
```bash
python test_player_detection.py
```

This will test:
- Configuration loading
- Player detection initialization
- Seat management setup
- Debug image generation

## Next Steps

1. **Fine-tune Positions**: Use the calibrator to adjust seat positions for your specific table
2. **Test with Live Game**: Run detection during actual gameplay to verify accuracy
3. **Adjust Thresholds**: Modify confidence thresholds in config if needed
4. **Color Calibration**: Adjust HSV ranges for button detection if colors vary

The system is now ready for live blackjack table monitoring with comprehensive player and card tracking!
