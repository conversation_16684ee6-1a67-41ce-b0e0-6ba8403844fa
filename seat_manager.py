"""
Seat management module for blackjack table
Handles card detection for specific player seats and dealer
"""

import cv2
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from config import PLAYER_SEATS, DEALER_CONFIG, CARD_DETECTION
from card_detector import CardDetector

class SeatManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.card_detector = CardDetector()
        
        # Track cards for each seat
        self.seat_cards = {}
        for seat_id in PLAYER_SEATS.keys():
            self.seat_cards[seat_id] = []
        
        # Track dealer cards
        self.dealer_cards = []
        
        # Card detection history to prevent duplicates
        self.detection_history = {}
        for seat_id in PLAYER_SEATS.keys():
            self.detection_history[seat_id] = []
        self.detection_history['dealer'] = []
    
    def detect_cards_in_seat(self, image: np.ndarray, seat_id: str) -> List[str]:
        """
        Detect cards in a specific player seat
        Returns list of newly detected cards
        """
        if seat_id not in PLAYER_SEATS:
            return []
        
        seat_config = PLAYER_SEATS[seat_id]
        card_regions = seat_config['card_regions']
        
        new_cards = []
        
        for i, region in enumerate(card_regions):
            x, y, w, h = region['x'], region['y'], region['width'], region['height']
            
            # Ensure coordinates are within image bounds
            if x + w > image.shape[1] or y + h > image.shape[0]:
                continue
            
            # Extract card region
            card_roi = image[y:y+h, x:x+w]
            
            if card_roi.size == 0:
                continue
            
            # Detect card in this specific region
            card_rank = self.card_detector.extract_card_rank(image, (x, y, w, h))
            
            if card_rank:
                # Check if this is a new card (not in recent history)
                if not self._is_duplicate_card(seat_id, card_rank, i):
                    new_cards.append(card_rank)
                    self.seat_cards[seat_id].append({
                        'rank': card_rank,
                        'position': i,
                        'region': region
                    })
                    
                    # Update detection history
                    self.detection_history[seat_id].append({
                        'rank': card_rank,
                        'position': i,
                        'timestamp': cv2.getTickCount()
                    })
                    
                    self.logger.info(f"New card detected in {seat_id} position {i}: {card_rank}")
        
        # Clean old detection history
        self._clean_detection_history(seat_id)
        
        return new_cards
    
    def detect_dealer_cards(self, image: np.ndarray) -> List[str]:
        """
        Detect cards in dealer area
        Returns list of newly detected cards
        """
        card_regions = DEALER_CONFIG['card_regions']
        new_cards = []
        
        for i, region in enumerate(card_regions):
            x, y, w, h = region['x'], region['y'], region['width'], region['height']
            
            # Ensure coordinates are within image bounds
            if x + w > image.shape[1] or y + h > image.shape[0]:
                continue
            
            # Extract card region
            card_roi = image[y:y+h, x:x+w]
            
            if card_roi.size == 0:
                continue
            
            # Detect card in this specific region
            card_rank = self.card_detector.extract_card_rank(image, (x, y, w, h))
            
            if card_rank:
                # Check if this is a new card (not in recent history)
                if not self._is_duplicate_card('dealer', card_rank, i):
                    new_cards.append(card_rank)
                    self.dealer_cards.append({
                        'rank': card_rank,
                        'position': i,
                        'region': region
                    })
                    
                    # Update detection history
                    self.detection_history['dealer'].append({
                        'rank': card_rank,
                        'position': i,
                        'timestamp': cv2.getTickCount()
                    })
                    
                    self.logger.info(f"New dealer card detected in position {i}: {card_rank}")
        
        # Clean old detection history
        self._clean_detection_history('dealer')
        
        return new_cards
    
    def _is_duplicate_card(self, seat_id: str, card_rank: str, position: int) -> bool:
        """
        Check if a card is a duplicate based on recent detection history
        """
        if seat_id not in self.detection_history:
            return False
        
        current_time = cv2.getTickCount()
        frequency = cv2.getTickFrequency()
        
        # Check recent detections (last 5 seconds)
        for detection in self.detection_history[seat_id][-10:]:
            time_diff = (current_time - detection['timestamp']) / frequency
            
            if (time_diff < 5.0 and 
                detection['rank'] == card_rank and 
                detection['position'] == position):
                return True
        
        return False
    
    def _clean_detection_history(self, seat_id: str):
        """
        Clean old entries from detection history
        """
        if seat_id not in self.detection_history:
            return
        
        current_time = cv2.getTickCount()
        frequency = cv2.getTickFrequency()
        
        # Keep only detections from last 30 seconds
        self.detection_history[seat_id] = [
            detection for detection in self.detection_history[seat_id]
            if (current_time - detection['timestamp']) / frequency < 30.0
        ]
        
        # Limit to last 50 detections
        if len(self.detection_history[seat_id]) > 50:
            self.detection_history[seat_id] = self.detection_history[seat_id][-50:]
    
    def get_seat_cards(self, seat_id: str) -> List[str]:
        """
        Get all cards currently detected for a seat
        """
        if seat_id not in self.seat_cards:
            return []
        
        return [card['rank'] for card in self.seat_cards[seat_id]]
    
    def get_dealer_cards(self) -> List[str]:
        """
        Get all dealer cards currently detected
        """
        return [card['rank'] for card in self.dealer_cards]
    
    def clear_seat_cards(self, seat_id: str):
        """
        Clear all cards for a specific seat
        """
        if seat_id in self.seat_cards:
            self.seat_cards[seat_id] = []
        if seat_id in self.detection_history:
            self.detection_history[seat_id] = []
    
    def clear_dealer_cards(self):
        """
        Clear all dealer cards
        """
        self.dealer_cards = []
        self.detection_history['dealer'] = []
    
    def clear_all_cards(self):
        """
        Clear all cards from all seats and dealer
        """
        for seat_id in PLAYER_SEATS.keys():
            self.clear_seat_cards(seat_id)
        self.clear_dealer_cards()
    
    def update_all_seats(self, image: np.ndarray) -> Dict[str, List[str]]:
        """
        Update card detection for all seats and dealer
        Returns dictionary with newly detected cards for each seat
        """
        all_new_cards = {}
        
        # Detect cards for each player seat
        for seat_id in PLAYER_SEATS.keys():
            new_cards = self.detect_cards_in_seat(image, seat_id)
            all_new_cards[seat_id] = new_cards
        
        # Detect dealer cards
        dealer_new_cards = self.detect_dealer_cards(image)
        all_new_cards['dealer'] = dealer_new_cards
        
        return all_new_cards
    
    def get_debug_image(self, image: np.ndarray) -> np.ndarray:
        """
        Get debug image with all card detection regions highlighted
        """
        debug_img = image.copy()
        
        # Draw player seat card regions
        for seat_id, seat_config in PLAYER_SEATS.items():
            card_regions = seat_config['card_regions']
            
            for i, region in enumerate(card_regions):
                x, y, w, h = region['x'], region['y'], region['width'], region['height']
                
                # Color based on whether card is detected
                has_card = any(card['position'] == i for card in self.seat_cards[seat_id])
                color = (0, 255, 0) if has_card else (255, 0, 0)
                
                cv2.rectangle(debug_img, (x, y), (x+w, y+h), color, 2)
                
                # Label the card position
                cv2.putText(debug_img, f"S{seat_id[-1]}C{i+1}", (x, y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                
                # Show detected card rank
                if has_card:
                    card = next(card for card in self.seat_cards[seat_id] if card['position'] == i)
                    cv2.putText(debug_img, card['rank'], (x+5, y+20), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Draw dealer card regions
        for i, region in enumerate(DEALER_CONFIG['card_regions']):
            x, y, w, h = region['x'], region['y'], region['width'], region['height']
            
            # Color based on whether card is detected
            has_card = any(card['position'] == i for card in self.dealer_cards)
            color = (0, 255, 255) if has_card else (0, 255, 255)  # Yellow for dealer
            
            cv2.rectangle(debug_img, (x, y), (x+w, y+h), color, 2)
            
            # Label the dealer card position
            cv2.putText(debug_img, f"DC{i+1}", (x, y-5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            
            # Show detected card rank
            if has_card:
                card = next(card for card in self.dealer_cards if card['position'] == i)
                cv2.putText(debug_img, card['rank'], (x+5, y+20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return debug_img
