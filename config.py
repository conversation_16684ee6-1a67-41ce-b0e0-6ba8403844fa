"""
Configuration settings for the Blackjack Card Counting Bot
"""

# Deck Configuration
TOTAL_DECKS = 8
CARDS_PER_DECK = 52
TOTAL_CARDS = TOTAL_DECKS * CARDS_PER_DECK

# Card Values for Hi-Lo Counting System
CARD_VALUES = {
    '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,  # Low cards: +1
    '7': 0, '8': 0, '9': 0,                   # Neutral cards: 0
    '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1  # High cards: -1
}

# Screen Capture Settings
SCREEN_REGION = {
    'top': 132,
    'left': 257,
    'width': 1405,
    'height': 907
}

# Card Detection Settings
CARD_DETECTION = {
    'min_card_area': 1000,  # Increased for better card detection
    'max_card_area': 12000,  # Increased for larger cards
    'card_aspect_ratio_min': 0.5,  # More lenient for card shapes
    'card_aspect_ratio_max': 1.0,  # Standard playing card ratio
    'confidence_threshold': 0.3  # Lower threshold for better detection
}

# GUI Settings
GUI_CONFIG = {
    'window_title': 'Blackjack Card Counter',
    'window_size': '800x600',
    'update_interval': 100,  # milliseconds
    'font_size': 11,
    'bg_color': '#e8e8e8',  # Uniform light gray
    'text_color': '#2c2c2c',  # Dark gray text
    'accent_color': '#5a5a5a',  # Medium gray for values
    'frame_color': '#d0d0d0',  # Slightly darker gray for frames
    'button_color': '#c0c0c0'  # Button gray
}

# OCR Settings
OCR_CONFIG = {
    'use_easyocr': True,  # Set to False to use Tesseract
    'languages': ['en'],
    'confidence_threshold': 0.5
}

# Card Template Paths (for template matching)
CARD_TEMPLATES = {
    'suits': ['hearts', 'diamonds', 'clubs', 'spades'],
    'ranks': ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
}

# Player Seat Configuration (relative to screen region)
PLAYER_SEATS = {
    'seat_1': {
        'name_region': {'x': 50, 'y': 650, 'width': 150, 'height': 40},
        'card_regions': [
            {'x': 80, 'y': 500, 'width': 90, 'height': 120},   # First card
            {'x': 100, 'y': 500, 'width': 90, 'height': 120},  # Second card (overlapped)
            {'x': 120, 'y': 500, 'width': 90, 'height': 120},  # Third card
            {'x': 140, 'y': 500, 'width': 90, 'height': 120},  # Fourth card
            {'x': 160, 'y': 500, 'width': 90, 'height': 120}   # Fifth card
        ],
        'action_region': {'x': 80, 'y': 620, 'width': 80, 'height': 80}
    },
    'seat_2': {
        'name_region': {'x': 300, 'y': 650, 'width': 150, 'height': 40},
        'card_regions': [
            {'x': 330, 'y': 500, 'width': 90, 'height': 120},
            {'x': 350, 'y': 500, 'width': 90, 'height': 120},
            {'x': 370, 'y': 500, 'width': 90, 'height': 120},
            {'x': 390, 'y': 500, 'width': 90, 'height': 120},
            {'x': 410, 'y': 500, 'width': 90, 'height': 120}
        ],
        'action_region': {'x': 330, 'y': 620, 'width': 80, 'height': 80}
    },
    'seat_3': {
        'name_region': {'x': 550, 'y': 650, 'width': 150, 'height': 40},
        'card_regions': [
            {'x': 580, 'y': 500, 'width': 90, 'height': 120},
            {'x': 600, 'y': 500, 'width': 90, 'height': 120},
            {'x': 620, 'y': 500, 'width': 90, 'height': 120},
            {'x': 640, 'y': 500, 'width': 90, 'height': 120},
            {'x': 660, 'y': 500, 'width': 90, 'height': 120}
        ],
        'action_region': {'x': 580, 'y': 620, 'width': 80, 'height': 80}
    },
    'seat_4': {
        'name_region': {'x': 800, 'y': 650, 'width': 150, 'height': 40},
        'card_regions': [
            {'x': 830, 'y': 500, 'width': 90, 'height': 120},
            {'x': 850, 'y': 500, 'width': 90, 'height': 120},
            {'x': 870, 'y': 500, 'width': 90, 'height': 120},
            {'x': 890, 'y': 500, 'width': 90, 'height': 120},
            {'x': 910, 'y': 500, 'width': 90, 'height': 120}
        ],
        'action_region': {'x': 830, 'y': 620, 'width': 80, 'height': 80}
    },
    'seat_5': {
        'name_region': {'x': 1050, 'y': 650, 'width': 150, 'height': 40},
        'card_regions': [
            {'x': 1080, 'y': 500, 'width': 90, 'height': 120},
            {'x': 1100, 'y': 500, 'width': 90, 'height': 120},
            {'x': 1120, 'y': 500, 'width': 90, 'height': 120},
            {'x': 1140, 'y': 500, 'width': 90, 'height': 120},
            {'x': 1160, 'y': 500, 'width': 90, 'height': 120}
        ],
        'action_region': {'x': 1080, 'y': 620, 'width': 80, 'height': 80}
    }
}

# Dealer Configuration
DEALER_CONFIG = {
    'card_regions': [
        {'x': 600, 'y': 200, 'width': 90, 'height': 120},  # First dealer card
        {'x': 620, 'y': 200, 'width': 90, 'height': 120},  # Second dealer card
        {'x': 640, 'y': 200, 'width': 90, 'height': 120},  # Third dealer card
        {'x': 660, 'y': 200, 'width': 90, 'height': 120},  # Fourth dealer card
        {'x': 680, 'y': 200, 'width': 90, 'height': 120}   # Fifth dealer card
    ]
}

# Player Detection Settings
PLAYER_DETECTION = {
    'empty_seat_threshold': 0.8,  # Similarity threshold for empty seat detection
    'name_confidence_threshold': 0.6,  # OCR confidence for player names
    'action_button_colors': {
        'hit': {'lower': [0, 100, 100], 'upper': [10, 255, 255]},      # Red range in HSV
        'stand': {'lower': [40, 100, 100], 'upper': [80, 255, 255]}    # Green range in HSV
    }
}

# Logging
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'card_counter.log'
}
