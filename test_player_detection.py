"""
Test script for the new player detection and seat management features
"""

import cv2
import numpy as np
from player_detector import <PERSON>Detector
from seat_manager import <PERSON>tManager
from config import PLAYER_SEATS, DEALER_CONFIG

def test_player_detection():
    """Test the player detection functionality"""
    print("Testing Player Detection System...")
    
    # Initialize detectors
    player_detector = PlayerDetector()
    seat_manager = SeatManager()
    
    print(f"Initialized detectors for {len(PLAYER_SEATS)} player seats")
    print(f"Player seats: {list(PLAYER_SEATS.keys())}")
    print(f"Dealer card regions: {len(DEALER_CONFIG['card_regions'])}")
    
    # Test with a dummy image (you can replace this with actual screen capture)
    dummy_image = np.zeros((900, 1400, 3), dtype=np.uint8)
    
    # Test player state detection
    print("\nTesting player state detection...")
    player_states = player_detector.update_all_players(dummy_image)
    
    for seat_id, state in player_states.items():
        print(f"{seat_id}: Occupied={state['occupied']}, Name={state['player_name']}, Action={state['action']}")
    
    # Test seat card detection
    print("\nTesting seat card detection...")
    all_new_cards = seat_manager.update_all_seats(dummy_image)
    
    for seat_id, cards in all_new_cards.items():
        print(f"{seat_id}: New cards detected: {cards}")
    
    # Test debug image generation
    print("\nTesting debug image generation...")
    player_debug = player_detector.get_debug_image(dummy_image)
    seat_debug = seat_manager.get_debug_image(dummy_image)
    
    if player_debug is not None:
        print("Player debug image generated successfully")
    if seat_debug is not None:
        print("Seat debug image generated successfully")
    
    print("\nPlayer detection test completed!")

def test_configuration():
    """Test the configuration settings"""
    print("Testing Configuration...")
    
    print(f"Total player seats configured: {len(PLAYER_SEATS)}")
    
    for seat_id, config in PLAYER_SEATS.items():
        print(f"\n{seat_id}:")
        print(f"  Name region: {config['name_region']}")
        print(f"  Card regions: {len(config['card_regions'])}")
        print(f"  Action region: {config['action_region']}")
    
    print(f"\nDealer configuration:")
    print(f"  Card regions: {len(DEALER_CONFIG['card_regions'])}")
    
    print("\nConfiguration test completed!")

if __name__ == "__main__":
    print("=== Player Detection System Test ===\n")
    
    try:
        test_configuration()
        print("\n" + "="*50 + "\n")
        test_player_detection()
        
        print("\n=== All Tests Completed Successfully! ===")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
