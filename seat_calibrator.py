"""
Seat position calibrator for blackjack table
Allows manual adjustment of seat positions to match actual table layout
"""

import cv2
import numpy as np
import json
from typing import Dict, <PERSON><PERSON>
from config import SCREEN_REGION, PLAYER_SEATS, DEALER_CONFIG

class SeatCalibrator:
    def __init__(self):
        self.current_seat = None
        self.current_region_type = None  # 'name', 'card', 'action'
        self.current_card_index = 0
        self.calibration_data = {}
        self.load_calibration()
        
    def load_calibration(self):
        """Load existing calibration data if available"""
        try:
            with open('seat_calibration.json', 'r') as f:
                self.calibration_data = json.load(f)
        except FileNotFoundError:
            # Use default configuration
            self.calibration_data = {
                'player_seats': PLAYER_SEATS.copy(),
                'dealer_config': DEALER_CONFIG.copy()
            }
    
    def save_calibration(self):
        """Save calibration data to file"""
        with open('seat_calibration.json', 'w') as f:
            json.dump(self.calibration_data, f, indent=2)
        print("Calibration saved to seat_calibration.json")
    
    def start_calibration(self):
        """Start interactive calibration process"""
        print("=== Seat Position Calibrator ===")
        print("Instructions:")
        print("- Click and drag to define regions")
        print("- Press 'n' for next seat/region")
        print("- Press 's' to save calibration")
        print("- Press 'q' to quit")
        print("- Press 'r' to reset current region")
        
        # Capture screen for calibration
        from card_detector import CardDetector
        detector = CardDetector()
        image = detector.capture_screen()
        
        if image is None:
            print("Failed to capture screen. Please check screen region settings.")
            return
        
        # Start with first seat
        self.current_seat = 'seat_1'
        self.current_region_type = 'name'
        self.current_card_index = 0
        
        cv2.namedWindow('Seat Calibrator', cv2.WINDOW_NORMAL)
        cv2.setMouseCallback('Seat Calibrator', self.mouse_callback)
        
        self.drawing = False
        self.start_point = None
        self.end_point = None
        
        while True:
            display_image = image.copy()
            self.draw_current_regions(display_image)
            self.draw_instructions(display_image)
            
            cv2.imshow('Seat Calibrator', display_image)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                self.save_calibration()
            elif key == ord('n'):
                self.next_region()
            elif key == ord('r'):
                self.reset_current_region()
        
        cv2.destroyAllWindows()
    
    def mouse_callback(self, event, x, y, flags, param):
        """Handle mouse events for region selection"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_point = (x, y)
        
        elif event == cv2.EVENT_MOUSEMOVE:
            if self.drawing:
                self.end_point = (x, y)
        
        elif event == cv2.EVENT_LBUTTONUP:
            self.drawing = False
            if self.start_point and self.end_point:
                self.update_current_region()
    
    def update_current_region(self):
        """Update the current region being calibrated"""
        if not self.start_point or not self.end_point:
            return
        
        x1, y1 = self.start_point
        x2, y2 = self.end_point
        
        # Ensure proper rectangle coordinates
        x = min(x1, x2)
        y = min(y1, y2)
        w = abs(x2 - x1)
        h = abs(y2 - y1)
        
        region = {'x': x, 'y': y, 'width': w, 'height': h}
        
        if self.current_seat == 'dealer':
            # Update dealer region
            if self.current_region_type == 'card':
                self.calibration_data['dealer_config']['card_regions'][self.current_card_index] = region
        else:
            # Update player seat region
            if self.current_seat not in self.calibration_data['player_seats']:
                self.calibration_data['player_seats'][self.current_seat] = {
                    'name_region': {},
                    'card_regions': [{} for _ in range(5)],
                    'action_region': {}
                }
            
            if self.current_region_type == 'name':
                self.calibration_data['player_seats'][self.current_seat]['name_region'] = region
            elif self.current_region_type == 'card':
                self.calibration_data['player_seats'][self.current_seat]['card_regions'][self.current_card_index] = region
            elif self.current_region_type == 'action':
                self.calibration_data['player_seats'][self.current_seat]['action_region'] = region
        
        print(f"Updated {self.current_seat} {self.current_region_type} region: {region}")
    
    def next_region(self):
        """Move to next region for calibration"""
        if self.current_seat == 'dealer':
            if self.current_region_type == 'card':
                if self.current_card_index < 4:
                    self.current_card_index += 1
                else:
                    print("Calibration complete!")
                    return
        else:
            if self.current_region_type == 'name':
                self.current_region_type = 'card'
                self.current_card_index = 0
            elif self.current_region_type == 'card':
                if self.current_card_index < 4:
                    self.current_card_index += 1
                else:
                    self.current_region_type = 'action'
            elif self.current_region_type == 'action':
                # Move to next seat
                seat_num = int(self.current_seat.split('_')[1])
                if seat_num < 5:
                    self.current_seat = f'seat_{seat_num + 1}'
                    self.current_region_type = 'name'
                    self.current_card_index = 0
                else:
                    # Move to dealer
                    self.current_seat = 'dealer'
                    self.current_region_type = 'card'
                    self.current_card_index = 0
        
        print(f"Now calibrating: {self.current_seat} {self.current_region_type} {self.current_card_index if self.current_region_type == 'card' else ''}")
    
    def reset_current_region(self):
        """Reset the current region to default"""
        print(f"Reset {self.current_seat} {self.current_region_type} region")
    
    def draw_current_regions(self, image):
        """Draw all currently defined regions"""
        # Draw player seat regions
        for seat_id, seat_config in self.calibration_data['player_seats'].items():
            color = (0, 255, 0) if seat_id == self.current_seat else (100, 100, 100)
            
            # Draw name region
            if 'name_region' in seat_config and seat_config['name_region']:
                region = seat_config['name_region']
                if 'x' in region:
                    cv2.rectangle(image, (region['x'], region['y']), 
                                (region['x'] + region['width'], region['y'] + region['height']), 
                                color, 2)
                    cv2.putText(image, f"{seat_id} name", (region['x'], region['y'] - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Draw card regions
            if 'card_regions' in seat_config:
                for i, region in enumerate(seat_config['card_regions']):
                    if region and 'x' in region:
                        card_color = (0, 255, 255) if (seat_id == self.current_seat and 
                                                     self.current_region_type == 'card' and 
                                                     i == self.current_card_index) else color
                        cv2.rectangle(image, (region['x'], region['y']), 
                                    (region['x'] + region['width'], region['y'] + region['height']), 
                                    card_color, 1)
                        cv2.putText(image, f"C{i+1}", (region['x'], region['y'] - 5),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.4, card_color, 1)
            
            # Draw action region
            if 'action_region' in seat_config and seat_config['action_region']:
                region = seat_config['action_region']
                if 'x' in region:
                    action_color = (255, 0, 255) if (seat_id == self.current_seat and 
                                                   self.current_region_type == 'action') else color
                    cv2.rectangle(image, (region['x'], region['y']), 
                                (region['x'] + region['width'], region['y'] + region['height']), 
                                action_color, 2)
                    cv2.putText(image, f"{seat_id} action", (region['x'], region['y'] - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, action_color, 1)
        
        # Draw dealer regions
        dealer_color = (255, 255, 0) if self.current_seat == 'dealer' else (100, 100, 100)
        for i, region in enumerate(self.calibration_data['dealer_config']['card_regions']):
            if region and 'x' in region:
                card_color = (0, 255, 255) if (self.current_seat == 'dealer' and 
                                             self.current_region_type == 'card' and 
                                             i == self.current_card_index) else dealer_color
                cv2.rectangle(image, (region['x'], region['y']), 
                            (region['x'] + region['width'], region['y'] + region['height']), 
                            card_color, 2)
                cv2.putText(image, f"Dealer C{i+1}", (region['x'], region['y'] - 5),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, card_color, 1)
        
        # Draw current selection rectangle
        if self.drawing and self.start_point and self.end_point:
            cv2.rectangle(image, self.start_point, self.end_point, (0, 0, 255), 2)
    
    def draw_instructions(self, image):
        """Draw current calibration instructions"""
        instruction = f"Calibrating: {self.current_seat} {self.current_region_type}"
        if self.current_region_type == 'card':
            instruction += f" {self.current_card_index + 1}"
        
        cv2.putText(image, instruction, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(image, "n=next, s=save, r=reset, q=quit", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

if __name__ == "__main__":
    calibrator = SeatCalibrator()
    calibrator.start_calibration()
