"""
Player detection module for blackjack table
Handles player seat detection, name recognition, and action button detection
"""

import cv2
import numpy as np
import easyocr
import logging
from typing import Dict, List, Optional, Tuple
from config import PLAYER_SEATS, PLAYER_DETECTION, OCR_CONFIG

class PlayerDetector:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_ocr()
        
        # Store empty seat templates for comparison
        self.empty_seat_templates = {}
        
        # Track player states
        self.player_states = {}
        for seat_id in PLAYER_SEATS.keys():
            self.player_states[seat_id] = {
                'occupied': False,
                'player_name': None,
                'action': None,  # 'hit', 'stand', or None
                'cards': []
            }
    
    def setup_ocr(self):
        """Initialize OCR engine for player name detection"""
        if OCR_CONFIG['use_easyocr']:
            self.ocr_reader = easyocr.Reader(OCR_CONFIG['languages'])
        else:
            import pytesseract
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    def detect_player_occupation(self, image: np.ndarray, seat_id: str) -> bool:
        """
        Detect if a player seat is occupied by checking for player name text
        """
        if seat_id not in PLAYER_SEATS:
            return False
        
        seat_config = PLAYER_SEATS[seat_id]
        name_region = seat_config['name_region']
        
        # Extract name region
        x, y, w, h = name_region['x'], name_region['y'], name_region['width'], name_region['height']
        
        # Ensure coordinates are within image bounds
        if x + w > image.shape[1] or y + h > image.shape[0]:
            return False
        
        name_roi = image[y:y+h, x:x+w]
        
        if name_roi.size == 0:
            return False
        
        # Check for text presence (player name)
        player_name = self.extract_player_name(name_roi)
        
        if player_name and len(player_name) > 2:  # Valid player name should be at least 3 characters
            self.player_states[seat_id]['occupied'] = True
            self.player_states[seat_id]['player_name'] = player_name
            return True
        else:
            self.player_states[seat_id]['occupied'] = False
            self.player_states[seat_id]['player_name'] = None
            return False
    
    def extract_player_name(self, name_roi: np.ndarray) -> Optional[str]:
        """Extract player name from the name region"""
        try:
            # Preprocess the name region for better OCR
            gray = cv2.cvtColor(name_roi, cv2.COLOR_BGR2GRAY) if len(name_roi.shape) == 3 else name_roi
            
            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Apply threshold to get clear text
            _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            if OCR_CONFIG['use_easyocr']:
                results = self.ocr_reader.readtext(thresh)
                if results:
                    # Get the text with highest confidence
                    best_result = max(results, key=lambda x: x[2])
                    if best_result[2] > PLAYER_DETECTION['name_confidence_threshold']:
                        return best_result[1].strip()
            else:
                import pytesseract
                text = pytesseract.image_to_string(
                    thresh, 
                    config='--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
                ).strip()
                if len(text) > 2:
                    return text
                    
        except Exception as e:
            self.logger.debug(f"Player name extraction failed: {e}")
            
        return None
    
    def detect_action_button(self, image: np.ndarray, seat_id: str) -> Optional[str]:
        """
        Detect if hit or stand button is pressed for a player
        """
        if seat_id not in PLAYER_SEATS:
            return None
        
        seat_config = PLAYER_SEATS[seat_id]
        action_region = seat_config['action_region']
        
        # Extract action region
        x, y, w, h = action_region['x'], action_region['y'], action_region['width'], action_region['height']
        
        # Ensure coordinates are within image bounds
        if x + w > image.shape[1] or y + h > image.shape[0]:
            return None
        
        action_roi = image[y:y+h, x:x+w]
        
        if action_roi.size == 0:
            return None
        
        # Convert to HSV for color detection
        hsv = cv2.cvtColor(action_roi, cv2.COLOR_BGR2HSV)
        
        # Check for hit button (red)
        hit_colors = PLAYER_DETECTION['action_button_colors']['hit']
        hit_lower = np.array(hit_colors['lower'])
        hit_upper = np.array(hit_colors['upper'])
        hit_mask = cv2.inRange(hsv, hit_lower, hit_upper)
        
        # Check for stand button (green)
        stand_colors = PLAYER_DETECTION['action_button_colors']['stand']
        stand_lower = np.array(stand_colors['lower'])
        stand_upper = np.array(stand_colors['upper'])
        stand_mask = cv2.inRange(hsv, stand_lower, stand_upper)
        
        # Count pixels for each color
        hit_pixels = cv2.countNonZero(hit_mask)
        stand_pixels = cv2.countNonZero(stand_mask)
        
        total_pixels = action_roi.shape[0] * action_roi.shape[1]
        hit_ratio = hit_pixels / total_pixels
        stand_ratio = stand_pixels / total_pixels
        
        # Determine action based on dominant color
        if hit_ratio > 0.3:  # At least 30% red pixels
            self.player_states[seat_id]['action'] = 'hit'
            return 'hit'
        elif stand_ratio > 0.3:  # At least 30% green pixels
            self.player_states[seat_id]['action'] = 'stand'
            return 'stand'
        else:
            self.player_states[seat_id]['action'] = None
            return None
    
    def get_player_states(self) -> Dict:
        """Get current states of all players"""
        return self.player_states.copy()
    
    def update_all_players(self, image: np.ndarray) -> Dict:
        """
        Update all player states from the current image
        Returns dictionary with updated player information
        """
        updated_states = {}
        
        for seat_id in PLAYER_SEATS.keys():
            # Check if seat is occupied
            occupied = self.detect_player_occupation(image, seat_id)
            
            # If occupied, check for action buttons
            action = None
            if occupied:
                action = self.detect_action_button(image, seat_id)
            
            updated_states[seat_id] = {
                'occupied': occupied,
                'player_name': self.player_states[seat_id]['player_name'],
                'action': action,
                'cards': self.player_states[seat_id]['cards']
            }
        
        return updated_states
    
    def get_debug_image(self, image: np.ndarray) -> np.ndarray:
        """
        Get debug image with player detection regions highlighted
        """
        debug_img = image.copy()
        
        for seat_id, seat_config in PLAYER_SEATS.items():
            # Draw name region
            name_region = seat_config['name_region']
            x, y, w, h = name_region['x'], name_region['y'], name_region['width'], name_region['height']
            
            # Color based on occupation status
            color = (0, 255, 0) if self.player_states[seat_id]['occupied'] else (0, 0, 255)
            cv2.rectangle(debug_img, (x, y), (x+w, y+h), color, 2)
            
            # Label the seat
            cv2.putText(debug_img, f"Seat {seat_id[-1]}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Show player name if detected
            if self.player_states[seat_id]['player_name']:
                cv2.putText(debug_img, self.player_states[seat_id]['player_name'], 
                           (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Draw action region
            action_region = seat_config['action_region']
            ax, ay, aw, ah = action_region['x'], action_region['y'], action_region['width'], action_region['height']
            
            # Color based on action
            action_color = (0, 255, 255)  # Yellow default
            if self.player_states[seat_id]['action'] == 'hit':
                action_color = (0, 0, 255)  # Red
            elif self.player_states[seat_id]['action'] == 'stand':
                action_color = (0, 255, 0)  # Green
            
            cv2.rectangle(debug_img, (ax, ay), (ax+aw, ay+ah), action_color, 2)
            
            # Show action if detected
            if self.player_states[seat_id]['action']:
                cv2.putText(debug_img, self.player_states[seat_id]['action'].upper(), 
                           (ax, ay-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, action_color, 2)
        
        return debug_img
